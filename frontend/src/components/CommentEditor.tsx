import { useState, useEffect, useCallback, useRef } from 'react';
import dynamic from 'next/dynamic';
import 'react-quill/dist/quill.snow.css';
import { ImageService } from '@/services/imageService';
import ImageOverlay from './ImageOverlay';

// Dynamically import ReactQuill to avoid SSR issues
const ReactQuill = dynamic(() => import('react-quill'), { ssr: false });

interface CommentEditorProps {
  initialValue?: string;
  onSave: (content: string) => void;
  onCancel?: () => void;
  placeholder?: string;
  buttonText?: string;
  showCancelButton?: boolean;
  clearOnSave?: boolean;
}

const CommentEditor = ({
  initialValue = '',
  onSave,
  onCancel,
  placeholder = 'Write a comment...',
  buttonText = 'Save',
  showCancelButton = true,
  clearOnSave = false,
}: CommentEditorProps) => {
  const [content, setContent] = useState(initialValue);
  const [mounted, setMounted] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [overlayImageUrl, setOverlayImageUrl] = useState<string | null>(null);
  const quillRef = useRef<any>(null);

  // Update content when initialValue changes
  useEffect(() => {
    setContent(initialValue);
  }, [initialValue]);

  useEffect(() => {
    setMounted(true);
  }, []);

  const handleSave = useCallback(() => {
    if (content.trim() === '<p><br></p>' || content.trim() === '') return;
    onSave(content);
    if (clearOnSave) {
      setContent('');
    }
  }, [content, onSave, clearOnSave]);

  // Handle keyboard events
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    // Check for Ctrl+Enter or Cmd+Enter (for Mac)
    if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
      event.preventDefault();
      handleSave();
    }
  }, [handleSave]);

  // Add keyboard event listener
  useEffect(() => {
    const handleGlobalKeyDown = (event: KeyboardEvent) => {
      // Only handle if the editor is focused
      if (document.activeElement?.closest('.comment-editor')) {
        handleKeyDown(event);
      }
    };

    document.addEventListener('keydown', handleGlobalKeyDown);
    return () => {
      document.removeEventListener('keydown', handleGlobalKeyDown);
    };
  }, [handleKeyDown]);

  // Handle image upload
  const handleImageUpload = useCallback(async (file: File) => {
    setIsUploading(true);
    try {
      const response = await ImageService.uploadImage(file);
      const imageUrl = ImageService.getImageUrl(response.id);

      // Insert image HTML directly into content
      const imageHtml = `<img src="${imageUrl}" alt="Uploaded image" style="max-width: 100%; height: auto; border-radius: 3px; margin: 4px 0;" />`;

      // Add image to current content
      setContent(prevContent => {
        if (!prevContent || prevContent === '<p><br></p>') {
          return `<p>${imageHtml}</p>`;
        }
        return prevContent + `<p>${imageHtml}</p>`;
      });
    } catch (error) {
      console.error('Failed to upload image:', error);
      alert('Failed to upload image. Please try again.');
    } finally {
      setIsUploading(false);
    }
  }, []);

  // Handle drag and drop
  const handleDrop = useCallback((e: DragEvent) => {
    e.preventDefault();
    const files = Array.from(e.dataTransfer?.files || []);
    const imageFiles = files.filter(file => file.type.startsWith('image/'));

    if (imageFiles.length > 0) {
      imageFiles.forEach(file => handleImageUpload(file));
    }
  }, [handleImageUpload]);

  const handleDragOver = useCallback((e: DragEvent) => {
    e.preventDefault();
  }, []);

  // Set up drag and drop listeners on the editor wrapper
  useEffect(() => {
    if (mounted) {
      const editorElement = document.querySelector('.comment-editor__quill');
      if (editorElement) {
        const handleImageClick = (e: Event) => {
          const target = e.target as HTMLElement;
          if (target.tagName === 'IMG') {
            const img = target as HTMLImageElement;
            setOverlayImageUrl(img.src);
          }
        };

        editorElement.addEventListener('drop', handleDrop);
        editorElement.addEventListener('dragover', handleDragOver);
        editorElement.addEventListener('click', handleImageClick);

        return () => {
          editorElement.removeEventListener('drop', handleDrop);
          editorElement.removeEventListener('dragover', handleDragOver);
          editorElement.removeEventListener('click', handleImageClick);
        };
      }
    }
  }, [mounted, handleDrop, handleDragOver]);

  const modules = {
    toolbar: {
      container: [
        ['bold', 'italic', 'underline', 'strike'],
        [{ 'list': 'ordered' }, { 'list': 'bullet' }],
        ['link', 'image'],
        ['clean']
      ],
      handlers: {
        image: () => {
          const input = document.createElement('input');
          input.setAttribute('type', 'file');
          input.setAttribute('accept', 'image/*');
          input.click();

          input.onchange = () => {
            const file = input.files?.[0];
            if (file) {
              handleImageUpload(file);
            }
          };
        },
      },
    },
  };

  const formats = [
    'bold', 'italic', 'underline', 'strike',
    'list', 'bullet',
    'link', 'image'
  ];

  if (!mounted) {
    return (
      <div className="comment-editor-placeholder">
        <div className="comment-editor-loading">Loading editor...</div>
      </div>
    );
  }

  return (
    <>
      <div className="comment-editor">
        {isUploading && (
          <div className="comment-editor__upload-indicator">
            <div className="comment-editor__spinner"></div>
            <span>Uploading image...</span>
          </div>
        )}
        <ReactQuill
          ref={quillRef}
          value={content}
          onChange={setContent}
          modules={modules}
          formats={formats}
          placeholder={placeholder}
          className="comment-editor__quill"
        />
        <div className="comment-editor__actions">
          <button
            className="comment-editor__save-btn"
            onClick={handleSave}
            disabled={content.trim() === '<p><br></p>' || content.trim() === '' || isUploading}
          >
            {buttonText}
          </button>
          {showCancelButton && onCancel && (
            <button
              className="comment-editor__cancel-btn"
              onClick={onCancel}
            >
              Cancel
            </button>
          )}
        </div>
      </div>
      {overlayImageUrl && (
        <ImageOverlay
          imageUrl={overlayImageUrl}
          onClose={() => setOverlayImageUrl(null)}
        />
      )}
    </>
  );
};

export default CommentEditor;
