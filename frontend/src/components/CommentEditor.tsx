import { useState, useEffect, useCallback, useRef } from 'react';
import dynamic from 'next/dynamic';
import 'react-quill/dist/quill.snow.css';
import { ImageService } from '@/services/imageService';
import ImageOverlay from './ImageOverlay';

// Dynamically import ReactQuill to avoid SSR issues
const ReactQuill = dynamic(() => import('react-quill'), { ssr: false });

interface CommentEditorProps {
  initialValue?: string;
  onSave: (content: string) => void;
  onCancel?: () => void;
  placeholder?: string;
  buttonText?: string;
  showCancelButton?: boolean;
}

const CommentEditor = ({
  initialValue = '',
  onSave,
  onCancel,
  placeholder = 'Write a comment...',
  buttonText = 'Save',
  showCancelButton = true,
}: CommentEditorProps) => {
  const [content, setContent] = useState(initialValue);
  const [mounted, setMounted] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [overlayImageUrl, setOverlayImageUrl] = useState<string | null>(null);
  const [quillInstance, setQuillInstance] = useState<any>(null);
  const quillRef = useRef<any>(null);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Get Quill instance when component is ready
  useEffect(() => {
    if (mounted && quillRef.current) {
      const timer = setTimeout(() => {
        if (quillRef.current) {
          const quill = quillRef.current.getEditor ? quillRef.current.getEditor() : quillRef.current;
          setQuillInstance(quill);
        }
      }, 100); // Small delay to ensure Quill is fully initialized

      return () => clearTimeout(timer);
    }
  }, [mounted]);

  const handleSave = useCallback(() => {
    if (content.trim() === '<p><br></p>' || content.trim() === '') return;
    onSave(content);
    setContent('');
  }, [content, onSave]);

  // Handle keyboard events
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    // Check for Ctrl+Enter or Cmd+Enter (for Mac)
    if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
      event.preventDefault();
      handleSave();
    }
  }, [handleSave]);

  // Add and remove keyboard event listener
  useEffect(() => {
    if (mounted) {
      document.addEventListener('keydown', handleKeyDown);
      return () => {
        document.removeEventListener('keydown', handleKeyDown);
      };
    }
  }, [mounted, handleKeyDown]);

  // Handle image upload
  const handleImageUpload = useCallback(async (file: File) => {
    if (!quillInstance) return;

    setIsUploading(true);
    try {
      const response = await ImageService.uploadImage(file);
      const range = quillInstance.getSelection();
      const imageUrl = ImageService.getImageUrl(response.id);

      // Insert image at cursor position
      quillInstance.insertEmbed(range ? range.index : 0, 'image', imageUrl);

      // Move cursor after the image
      if (range) {
        quillInstance.setSelection(range.index + 1);
      }
    } catch (error) {
      console.error('Failed to upload image:', error);
      alert('Failed to upload image. Please try again.');
    } finally {
      setIsUploading(false);
    }
  }, [quillInstance]);

  // Handle drag and drop
  const handleDrop = useCallback((e: DragEvent) => {
    e.preventDefault();
    const files = Array.from(e.dataTransfer?.files || []);
    const imageFiles = files.filter(file => file.type.startsWith('image/'));

    if (imageFiles.length > 0) {
      imageFiles.forEach(file => handleImageUpload(file));
    }
  }, [handleImageUpload]);

  const handleDragOver = useCallback((e: DragEvent) => {
    e.preventDefault();
  }, []);

  // Set up drag and drop listeners
  useEffect(() => {
    if (mounted && quillRef.current) {
      const editor = quillRef.current.getEditor ? quillRef.current.getEditor() : quillRef.current;
      const container = editor.container;

      container.addEventListener('drop', handleDrop);
      container.addEventListener('dragover', handleDragOver);

      return () => {
        container.removeEventListener('drop', handleDrop);
        container.removeEventListener('dragover', handleDragOver);
      };
    }
  }, [mounted, handleDrop, handleDragOver]);

  // Handle image clicks for overlay
  const handleImageClick = useCallback((e: Event) => {
    const target = e.target as HTMLElement;
    if (target.tagName === 'IMG') {
      const img = target as HTMLImageElement;
      setOverlayImageUrl(img.src);
    }
  }, []);

  // Set up image click listeners
  useEffect(() => {
    if (mounted && quillRef.current) {
      const editor = quillRef.current.getEditor ? quillRef.current.getEditor() : quillRef.current;
      const container = editor.container;

      container.addEventListener('click', handleImageClick);

      return () => {
        container.removeEventListener('click', handleImageClick);
      };
    }
  }, [mounted, handleImageClick]);

  const modules = {
    toolbar: {
      container: [
        ['bold', 'italic', 'underline', 'strike'],
        [{ 'list': 'ordered' }, { 'list': 'bullet' }],
        ['link', 'image'],
        ['clean']
      ],
      handlers: {
        image: () => {
          const input = document.createElement('input');
          input.setAttribute('type', 'file');
          input.setAttribute('accept', 'image/*');
          input.click();

          input.onchange = () => {
            const file = input.files?.[0];
            if (file) {
              handleImageUpload(file);
            }
          };
        },
      },
    },
  };

  const formats = [
    'bold', 'italic', 'underline', 'strike',
    'list', 'bullet',
    'link', 'image'
  ];

  if (!mounted) {
    return (
      <div className="comment-editor-placeholder">
        <div className="comment-editor-loading">Loading editor...</div>
      </div>
    );
  }

  return (
    <>
      <div className="comment-editor">
        {isUploading && (
          <div className="comment-editor__upload-indicator">
            <div className="comment-editor__spinner"></div>
            <span>Uploading image...</span>
          </div>
        )}
        <ReactQuill
          ref={quillRef}
          value={content}
          onChange={setContent}
          modules={modules}
          formats={formats}
          placeholder={placeholder}
          className="comment-editor__quill"
        />
        <div className="comment-editor__actions">
          <button
            className="comment-editor__save-btn"
            onClick={handleSave}
            disabled={content.trim() === '<p><br></p>' || content.trim() === '' || isUploading}
          >
            {buttonText}
          </button>
          {showCancelButton && onCancel && (
            <button
              className="comment-editor__cancel-btn"
              onClick={onCancel}
            >
              Cancel
            </button>
          )}
        </div>
      </div>
      {overlayImageUrl && (
        <ImageOverlay
          imageUrl={overlayImageUrl}
          onClose={() => setOverlayImageUrl(null)}
        />
      )}
    </>
  );
};

export default CommentEditor;
