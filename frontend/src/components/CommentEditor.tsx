import { useState, useEffect, useCallback, useRef } from 'react';
import dynamic from 'next/dynamic';
import 'react-quill/dist/quill.snow.css';
import { ImageService } from '@/services/imageService';
import ImageOverlay from './ImageOverlay';

// Dynamically import ReactQuill to avoid SSR issues
const ReactQuill = dynamic(() => import('react-quill'), { ssr: false });

interface CommentEditorProps {
  initialValue?: string;
  onSave: (content: string) => void;
  onCancel?: () => void;
  placeholder?: string;
  buttonText?: string;
  showCancelButton?: boolean;
}

const CommentEditor = ({
  initialValue = '',
  onSave,
  onCancel,
  placeholder = 'Write a comment...',
  buttonText = 'Save',
  showCancelButton = true,
}: CommentEditorProps) => {
  const [content, setContent] = useState(initialValue);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const handleSave = useCallback(() => {
    if (content.trim() === '<p><br></p>' || content.trim() === '') return;
    onSave(content);
    setContent('');
  }, [content, onSave]);

  // Handle keyboard events
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    // Check for Ctrl+Enter or Cmd+Enter (for Mac)
    if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
      event.preventDefault();
      handleSave();
    }
  }, [handleSave]);

  // Add and remove keyboard event listener
  useEffect(() => {
    if (mounted) {
      document.addEventListener('keydown', handleKeyDown);
      return () => {
        document.removeEventListener('keydown', handleKeyDown);
      };
    }
  }, [mounted, handleKeyDown]);

  const modules = {
    toolbar: [
      ['bold', 'italic', 'underline', 'strike'],
      [{ 'list': 'ordered' }, { 'list': 'bullet' }],
      ['link'],
      ['clean']
    ],
  };

  const formats = [
    'bold', 'italic', 'underline', 'strike',
    'list', 'bullet',
    'link'
  ];

  if (!mounted) {
    return (
      <div className="comment-editor-placeholder">
        <div className="comment-editor-loading">Loading editor...</div>
      </div>
    );
  }

  return (
    <div className="comment-editor">
      <ReactQuill
        value={content}
        onChange={setContent}
        modules={modules}
        formats={formats}
        placeholder={placeholder}
        className="comment-editor__quill"
      />
      <div className="comment-editor__actions">
        <button
          className="comment-editor__save-btn"
          onClick={handleSave}
          disabled={content.trim() === '<p><br></p>' || content.trim() === ''}
        >
          {buttonText}
        </button>
        {showCancelButton && onCancel && (
          <button
            className="comment-editor__cancel-btn"
            onClick={onCancel}
          >
            Cancel
          </button>
        )}
      </div>
    </div>
  );
};

export default CommentEditor;
