const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4544';

export interface ImageUploadResponse {
  id: string;
  url: string;
}

export class ImageService {
  static async uploadImage(file: File): Promise<ImageUploadResponse> {
    const formData = new FormData();
    formData.append('image', file);

    const response = await fetch(`${API_BASE_URL}/images/upload`, {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || 'Failed to upload image');
    }

    return response.json();
  }

  static getImageUrl(imageId: string): string {
    return `${API_BASE_URL}/images/${imageId}`;
  }

  static async deleteImage(imageId: string): Promise<void> {
    const response = await fetch(`${API_BASE_URL}/images/${imageId}`, {
      method: 'DELETE',
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || 'Failed to delete image');
    }
  }
}
