import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Image } from './schemas/image.schema';

@Injectable()
export class ImageService {
  constructor(
    @InjectModel(Image.name) private imageModel: Model<Image>,
  ) {}

  async create(file: Express.Multer.File): Promise<Image> {
    const image = new this.imageModel({
      filename: file.filename || `${Date.now()}-${file.originalname}`,
      originalName: file.originalname,
      mimeType: file.mimetype,
      size: file.size,
      data: file.buffer,
    });

    return image.save();
  }

  async findOne(id: string): Promise<Image> {
    const image = await this.imageModel.findById(id).exec();
    if (!image) {
      throw new NotFoundException(`Image with ID ${id} not found`);
    }
    return image;
  }

  async findAll(): Promise<Image[]> {
    return this.imageModel.find().select('-data').exec(); // Exclude binary data for listing
  }

  async remove(id: string): Promise<Image> {
    const image = await this.imageModel.findByIdAndDelete(id).exec();
    if (!image) {
      throw new NotFoundException(`Image with ID ${id} not found`);
    }
    return image;
  }
}
