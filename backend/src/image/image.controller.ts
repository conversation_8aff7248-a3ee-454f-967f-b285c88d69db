import {
  <PERSON>,
  Post,
  Get,
  Param,
  Delete,
  UseInterceptors,
  UploadedFile,
  Res,
  BadRequestException,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { Response } from 'express';
import { ImageService } from './image.service';
import { Image } from './schemas/image.schema';

@Controller('images')
export class ImageController {
  constructor(private readonly imageService: ImageService) {}

  @Post('upload')
  @UseInterceptors(
    FileInterceptor('image', {
      limits: {
        fileSize: 10 * 1024 * 1024, // 10MB limit
      },
      fileFilter: (req, file, callback) => {
        if (!file.mimetype.match(/\/(jpg|jpeg|png|gif|webp)$/)) {
          return callback(new BadRequestException('Only image files are allowed!'), false);
        }
        callback(null, true);
      },
    }),
  )
  async uploadImage(@UploadedFile() file: Express.Multer.File): Promise<{ id: string; url: string }> {
    if (!file) {
      throw new BadRequestException('No file uploaded');
    }

    const image = await this.imageService.create(file);
    return {
      id: image._id,
      url: `/images/${image._id}`,
    };
  }

  @Get(':id')
  async getImage(@Param('id') id: string, @Res() res: Response): Promise<void> {
    const image = await this.imageService.findOne(id);
    
    res.set({
      'Content-Type': image.mimeType,
      'Content-Length': image.size.toString(),
      'Cache-Control': 'public, max-age=31536000', // Cache for 1 year
    });
    
    res.send(image.data);
  }

  @Get()
  async getAllImages(): Promise<Omit<Image, 'data'>[]> {
    return this.imageService.findAll();
  }

  @Delete(':id')
  async deleteImage(@Param('id') id: string): Promise<{ message: string }> {
    await this.imageService.remove(id);
    return { message: 'Image deleted successfully' };
  }
}
