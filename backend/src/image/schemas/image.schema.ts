import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { Field, ID, ObjectType } from '@nestjs/graphql';

@ObjectType()
@Schema({ timestamps: true })
export class Image extends Document {
  @Field(() => ID)
  _id: string;

  @Field()
  @Prop({ required: true })
  filename: string;

  @Field()
  @Prop({ required: true })
  originalName: string;

  @Field()
  @Prop({ required: true })
  mimeType: string;

  @Field()
  @Prop({ required: true })
  size: number;

  @Prop({ required: true })
  data: Buffer;

  @Field()
  @Prop({ type: Date, default: Date.now })
  createdAt: Date;

  @Field()
  @Prop({ type: Date, default: Date.now })
  updatedAt: Date;
}

export const ImageSchema = SchemaFactory.createForClass(Image);
